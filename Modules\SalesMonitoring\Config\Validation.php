<?php

namespace Modules\SalesMonitoring\Config;

use CodeIgniter\Config\BaseConfig;

class Validation extends BaseConfig
{
    // Common fields for all sale types
    public array $exportSalePost = [
        'sale_type' => 'required|in_list[export,domestic,retail]',
        'product_category' => 'required|min_length[1]|max_length[100]',
        'sale_date' => 'required|valid_date[Y-m-d]',
        'buyer_name' => 'required|min_length[2]|max_length[255]',
        'country_export' => 'required|integer|greater_than[0]',
        'status' => 'required|in_list[booked,under negotiation]',
        'cost' => 'required|decimal|greater_than[0]',
        'notes' => 'permit_empty|max_length[1000]',
    ];

    public array $domesticSalePost = [
        'sale_type' => 'required|in_list[export,domestic,retail]',
        'product_category' => 'required|min_length[1]|max_length[100]',
        'sale_date' => 'required|valid_date[Y-m-d]',
        'buyer_name' => 'required|min_length[2]|max_length[255]',
        'buyer_type' => 'required|min_length[2]|max_length[100]',
        'status' => 'required|in_list[booked,under negotiation]',
        'cost' => 'required|decimal|greater_than[0]',
        'notes' => 'permit_empty|max_length[1000]',
    ];

    public array $retailSalePost = [
        'sale_type' => 'required|in_list[export,domestic,retail]',
        'product_category' => 'required|min_length[1]|max_length[100]',
        'sale_date' => 'required|valid_date[Y-m-d]',
        'buyer_name' => 'required|min_length[2]|max_length[255]',
        'buyer_type' => 'required|min_length[2]|max_length[100]',
        'status' => 'required|in_list[booked]',
        'cost' => 'required|decimal|greater_than[0]',
        'notes' => 'permit_empty|max_length[1000]',
    ];

    // Error messages for export sales
    public array $exportSalePost_errors = [
        'sale_type' => [
            'required' => 'Sale type is required.',
            'in_list' => 'Please select a valid sale type.',
        ],
        'product_category' => [
            'required' => 'Product category is required.',
            'min_length' => 'Product category must be at least 1 character.',
            'max_length' => 'Product category cannot exceed 100 characters.',
        ],
        'sale_date' => [
            'required' => 'Sale date is required.',
            'valid_date' => 'Please enter a valid date.',
        ],
        'buyer_name' => [
            'required' => 'Buyer name is required.',
            'min_length' => 'Buyer name must be at least 2 characters.',
            'max_length' => 'Buyer name cannot exceed 255 characters.',
        ],
        'country_export' => [
            'required' => 'Export country is required.',
            'integer' => 'Please select a valid country.',
            'greater_than' => 'Please select a valid country.',
        ],
        'status' => [
            'required' => 'Sale status is required.',
            'in_list' => 'Please select a valid status.',
        ],
        'cost' => [
            'required' => 'Cost is required.',
            'decimal' => 'Cost must be a valid number.',
            'greater_than' => 'Cost must be greater than 0.',
        ],
        'notes' => [
            'max_length' => 'Notes cannot exceed 1000 characters.',
        ],
    ];

    // Error messages for domestic sales
    public array $domesticSalePost_errors = [
        'sale_type' => [
            'required' => 'Sale type is required.',
            'in_list' => 'Please select a valid sale type.',
        ],
        'product_category' => [
            'required' => 'Product category is required.',
            'min_length' => 'Product category must be at least 1 character.',
            'max_length' => 'Product category cannot exceed 100 characters.',
        ],
        'sale_date' => [
            'required' => 'Sale date is required.',
            'valid_date' => 'Please enter a valid date.',
        ],
        'buyer_name' => [
            'required' => 'Buyer name is required.',
            'min_length' => 'Buyer name must be at least 2 characters.',
            'max_length' => 'Buyer name cannot exceed 255 characters.',
        ],
        'buyer_type' => [
            'required' => 'Buyer type is required.',
            'min_length' => 'Buyer type must be at least 2 characters.',
            'max_length' => 'Buyer type cannot exceed 100 characters.',
        ],
        'status' => [
            'required' => 'Sale status is required.',
            'in_list' => 'Please select a valid status.',
        ],
        'cost' => [
            'required' => 'Cost is required.',
            'decimal' => 'Cost must be a valid number.',
            'greater_than' => 'Cost must be greater than 0.',
        ],
        'notes' => [
            'max_length' => 'Notes cannot exceed 1000 characters.',
        ],
    ];

    // Error messages for retail sales
    public array $retailSalePost_errors = [
        'sale_type' => [
            'required' => 'Sale type is required.',
            'in_list' => 'Please select a valid sale type.',
        ],
        'product_category' => [
            'required' => 'Product category is required.',
            'min_length' => 'Product category must be at least 1 character.',
            'max_length' => 'Product category cannot exceed 100 characters.',
        ],
        'sale_date' => [
            'required' => 'Sale date is required.',
            'valid_date' => 'Please enter a valid date.',
        ],
        'buyer_name' => [
            'required' => 'Buyer name is required.',
            'min_length' => 'Buyer name must be at least 2 characters.',
            'max_length' => 'Buyer name cannot exceed 255 characters.',
        ],
        'buyer_type' => [
            'required' => 'Buyer type is required.',
            'min_length' => 'Buyer type must be at least 2 characters.',
            'max_length' => 'Buyer type cannot exceed 100 characters.',
        ],
        'status' => [
            'required' => 'Sale status is required.',
            'in_list' => 'Status must be "booked" for retail sales.',
        ],
        'cost' => [
            'required' => 'Cost is required.',
            'decimal' => 'Cost must be a valid number.',
            'greater_than' => 'Cost must be greater than 0.',
        ],
        'notes' => [
            'max_length' => 'Notes cannot exceed 1000 characters.',
        ],
    ];
}
