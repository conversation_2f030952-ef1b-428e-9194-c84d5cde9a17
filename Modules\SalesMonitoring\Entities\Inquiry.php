<?php

namespace Modules\SalesMonitoring\Entities;

use CodeIgniter\Entity\Entity;

class Inquiry extends Entity
{
    protected $datamap = [];
    protected $dates   = ['date_added', 'date_modified', 'deleted_at'];
    protected $casts   = [
        'id' => 'integer',
        'buyers_met_no' => 'integer',
        'inquiry_no' => 'integer',
        'date' => 'datetime'
    ];

    // Mutators for data formatting
    public function setDate(?string $date = null)
    {
        if ($date) {
            $this->attributes['date'] = date('Y-m-d', strtotime($date));
        }
        return $this;
    }

    // Accessors for data retrieval
    public function getFormattedDate()
    {
        if (isset($this->attributes['date'])) {
            return date('Y-m-d', strtotime($this->attributes['date']));
        }
        return null;
    }
}
