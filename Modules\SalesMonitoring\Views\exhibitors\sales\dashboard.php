<?= $this->extend('Modules\SalesMonitoring\Views\layouts\sales_layout') ?>

<?= $this->section('title') ?>Sales Dashboard - Trade Expo<?= $this->endSection() ?>

<?= $this->section('page_title') ?>Sales Monitoring<?= $this->endSection() ?>
<?= $this->section('page_subtitle') ?><?= $description ?> Performance Overview<?= $this->endSection() ?>

<?= $this->section('page_actions') ?>
<button type="button" class="btn btn-primary" onclick="openSalesDrawer()" data-aos="fade-left">
    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><line x1="12" y1="5" x2="12" y2="19"/><line x1="5" y1="12" x2="19" y2="12"/></svg>
    Add Sales
</button>
<button type="button" class="btn btn-outline-primary" onclick="openInquiryDrawer()" data-aos="fade-left" data-aos-delay="100">
    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><circle cx="9" cy="7" r="4"/><path d="M3 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2"/><path d="M16 3.13a4 4 0 0 1 0 7.75"/><path d="M21 21v-2a4 4 0 0 0 -3 -3.85"/></svg>
    Inquiries
</button>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Sales Statistics Cards -->
<div class="row row-deck row-cards mb-4">
    <div class="col-sm-6 col-lg-3">
        <div class="card sales-card" data-aos="fade-up" data-aos-delay="100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="subheader">Export Sales</div>
                    <div class="ms-auto">
                        <div class="metric-icon bg-primary text-white">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><circle cx="12" cy="12" r="9"/><path d="M12 3c7.2 0 9 1.8 9 9s-1.8 9 -9 9s-9 -1.8 -9 -9s1.8 -9 9 -9z"/><path d="M8 12h8"/><path d="M12 8v8"/></svg>
                        </div>
                    </div>
                </div>
                <div class="h1 mb-3 text-primary">$52,300</div>
                <div class="d-flex mb-2">
                    <div>Booked Sales</div>
                    <div class="ms-auto">
                        <span class="text-green d-inline-flex align-items-center lh-1">
                            12%
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-1" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><polyline points="3,17 9,11 13,15 21,7"/><polyline points="14,7 21,7 21,14"/></svg>
                        </span>
                    </div>
                </div>
                <div class="progress progress-sm">
                    <div class="progress-bar bg-primary" style="width: 75%" role="progressbar"></div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-sm-6 col-lg-3">
        <div class="card sales-card" data-aos="fade-up" data-aos-delay="200">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="subheader">Export Negotiations</div>
                    <div class="ms-auto">
                        <div class="metric-icon bg-warning text-white">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M8 9h8"/><path d="M8 13h6"/><path d="M18 4a3 3 0 0 1 3 3v8a3 3 0 0 1 -3 3h-5l-5 3v-3h-2a3 3 0 0 1 -3 -3v-8a3 3 0 0 1 3 -3h12z"/></svg>
                        </div>
                    </div>
                </div>
                <div class="h1 mb-3 text-warning">$7,890</div>
                <div class="d-flex mb-2">
                    <div>Under Negotiation</div>
                    <div class="ms-auto">
                        <span class="text-yellow d-inline-flex align-items-center lh-1">
                            5%
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-1" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><line x1="5" y1="12" x2="19" y2="12"/></svg>
                        </span>
                    </div>
                </div>
                <div class="progress progress-sm">
                    <div class="progress-bar bg-warning" style="width: 45%" role="progressbar"></div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-sm-6 col-lg-3">
        <div class="card sales-card" data-aos="fade-up" data-aos-delay="300">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="subheader">Domestic Sales</div>
                    <div class="ms-auto">
                        <div class="metric-icon bg-success text-white">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><polyline points="5 12 3 12 12 3 21 12 19 12"/><path d="M5 12v7a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-7"/><path d="M9 21v-6a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v6"/></svg>
                        </div>
                    </div>
                </div>
                <div class="h1 mb-3 text-success">₱24,300</div>
                <div class="d-flex mb-2">
                    <div>Local Market</div>
                    <div class="ms-auto">
                        <span class="text-green d-inline-flex align-items-center lh-1">
                            8%
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-1" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><polyline points="3,17 9,11 13,15 21,7"/><polyline points="14,7 21,7 21,14"/></svg>
                        </span>
                    </div>
                </div>
                <div class="progress progress-sm">
                    <div class="progress-bar bg-success" style="width: 60%" role="progressbar"></div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-sm-6 col-lg-3">
        <div class="card sales-card" data-aos="fade-up" data-aos-delay="400">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="subheader">Retail Sales</div>
                    <div class="ms-auto">
                        <div class="metric-icon bg-info text-white">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><circle cx="6" cy="19" r="2"/><circle cx="17" cy="19" r="2"/><path d="M17 17h-11v-14h-2"/><path d="M6 5l14 1l-1 7h-13"/></svg>
                        </div>
                    </div>
                </div>
                <div class="h1 mb-3 text-info">₱18,500</div>
                <div class="d-flex mb-2">
                    <div>Direct Sales</div>
                    <div class="ms-auto">
                        <span class="text-green d-inline-flex align-items-center lh-1">
                            15%
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-1" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><polyline points="3,17 9,11 13,15 21,7"/><polyline points="14,7 21,7 21,14"/></svg>
                        </span>
                    </div>
                </div>
                <div class="progress progress-sm">
                    <div class="progress-bar bg-info" style="width: 80%" role="progressbar"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Additional Metrics -->
<div class="row row-deck row-cards mb-4">
    <div class="col-sm-6 col-lg-3">
        <div class="card sales-card" data-aos="fade-up" data-aos-delay="500">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="subheader">Buyers Met</div>
                    <div class="ms-auto">
                        <div class="metric-icon bg-purple text-white">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><circle cx="9" cy="7" r="4"/><path d="M3 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2"/><path d="M16 3.13a4 4 0 0 1 0 7.75"/><path d="M21 21v-2a4 4 0 0 0 -3 -3.85"/></svg>
                        </div>
                    </div>
                </div>
                <div class="h1 mb-3 text-purple">52</div>
                <div class="d-flex mb-2">
                    <div>Total Contacts</div>
                    <div class="ms-auto">
                        <span class="text-green d-inline-flex align-items-center lh-1">
                            +8
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-1" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><polyline points="3,17 9,11 13,15 21,7"/><polyline points="14,7 21,7 21,14"/></svg>
                        </span>
                    </div>
                </div>
                <div class="progress progress-sm">
                    <div class="progress-bar bg-purple" style="width: 85%" role="progressbar"></div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-sm-6 col-lg-3">
        <div class="card sales-card" data-aos="fade-up" data-aos-delay="600">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="subheader">Active Inquiries</div>
                    <div class="ms-auto">
                        <div class="metric-icon bg-orange text-white">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M8 9h8"/><path d="M8 13h6"/><path d="M18 4a3 3 0 0 1 3 3v8a3 3 0 0 1 -3 3h-5l-5 3v-3h-2a3 3 0 0 1 -3 -3v-8a3 3 0 0 1 3 -3h12z"/></svg>
                        </div>
                    </div>
                </div>
                <div class="h1 mb-3 text-orange">22</div>
                <div class="d-flex mb-2">
                    <div>Pending Follow-up</div>
                    <div class="ms-auto">
                        <span class="text-yellow d-inline-flex align-items-center lh-1">
                            3 new
                        </span>
                    </div>
                </div>
                <div class="progress progress-sm">
                    <div class="progress-bar bg-orange" style="width: 65%" role="progressbar"></div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-sm-6 col-lg-3">
        <div class="card sales-card" data-aos="fade-up" data-aos-delay="700">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="subheader">Conversion Rate</div>
                    <div class="ms-auto">
                        <div class="metric-icon bg-teal text-white">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><line x1="4" y1="19" x2="20" y2="19"/><polyline points="4,15 8,9 12,11 16,6 20,10"/></svg>
                        </div>
                    </div>
                </div>
                <div class="h1 mb-3 text-teal">68%</div>
                <div class="d-flex mb-2">
                    <div>Inquiry to Sale</div>
                    <div class="ms-auto">
                        <span class="text-green d-inline-flex align-items-center lh-1">
                            +5%
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-1" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><polyline points="3,17 9,11 13,15 21,7"/><polyline points="14,7 21,7 21,14"/></svg>
                        </span>
                    </div>
                </div>
                <div class="progress progress-sm">
                    <div class="progress-bar bg-teal" style="width: 68%" role="progressbar"></div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-sm-6 col-lg-3">
        <div class="card sales-card" data-aos="fade-up" data-aos-delay="800">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="subheader">Total Revenue</div>
                    <div class="ms-auto">
                        <div class="metric-icon bg-dark text-white">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><circle cx="12" cy="12" r="9"/><path d="M14.8 9a2 2 0 0 0 -1.8 -1h-2a2 2 0 0 0 0 4h2a2 2 0 0 1 0 4h-2a2 2 0 0 1 -1.8 -1"/><path d="M12 6v2m0 8v2"/></svg>
                        </div>
                    </div>
                </div>
                <div class="h1 mb-3 text-dark">$95,190</div>
                <div class="d-flex mb-2">
                    <div>Combined Sales</div>
                    <div class="ms-auto">
                        <span class="text-green d-inline-flex align-items-center lh-1">
                            +18%
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-1" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><polyline points="3,17 9,11 13,15 21,7"/><polyline points="14,7 21,7 21,14"/></svg>
                        </span>
                    </div>
                </div>
                <div class="progress progress-sm">
                    <div class="progress-bar bg-dark" style="width: 90%" role="progressbar"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sales DataTable -->
<div class="card" data-aos="fade-up" data-aos-delay="900">
    <div class="card-header">
        <h3 class="card-title">Sales Records</h3>
        <div class="card-actions">
            <div class="dropdown">
                <a href="#" class="btn-action dropdown-toggle" data-bs-toggle="dropdown">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><circle cx="12" cy="12" r="1"/><circle cx="12" cy="19" r="1"/><circle cx="12" cy="5" r="1"/></svg>
                </a>
                <div class="dropdown-menu dropdown-menu-end">
                    <a href="#" class="dropdown-item" onclick="exportToExcel()">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M14 3v4a1 1 0 0 0 1 1h4"/><path d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z"/><path d="M8 11h8v7h-8z"/><path d="M8 15h8"/><path d="M11 11v7"/></svg>
                        Export to Excel
                    </a>
                    <a href="#" class="dropdown-item" onclick="exportToPDF()">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M14 3v4a1 1 0 0 0 1 1h4"/><path d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z"/><path d="M9 17h6"/><path d="M9 13h6"/></svg>
                        Export to PDF
                    </a>
                    <div class="dropdown-divider"></div>
                    <a href="#" class="dropdown-item" onclick="refreshSalesTable()">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4"/><path d="M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4"/></svg>
                        Refresh Data
                    </a>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        <!-- Filter Controls -->
        <div class="row mb-3">
            <div class="col-md-3">
                <label class="form-label">Sale Type</label>
                <select class="form-select" id="typeFilter">
                    <option value="">All Types</option>
                    <option value="export">Export</option>
                    <option value="domestic">Domestic</option>
                    <option value="retail">Retail</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Status</label>
                <select class="form-select" id="statusFilter">
                    <option value="">All Status</option>
                    <option value="booked">Booked</option>
                    <option value="under negotiation">Under Negotiation</option>
                    <option value="considered booked">Considered Booked</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Date From</label>
                <input type="date" class="form-control" id="dateFromFilter">
            </div>
            <div class="col-md-3">
                <label class="form-label">Date To</label>
                <input type="date" class="form-control" id="dateToFilter">
            </div>
        </div>
    </div>
    <div class="table-responsive">
        <table class="table table-vcenter table-sales card-table" id="salesTable">
            <thead>
                <tr>
                    <th>#</th>
                    <th>Date</th>
                    <th>Client</th>
                    <th>Product</th>
                    <th>Type</th>
                    <th>Amount</th>
                    <th>Status</th>
                    <th class="w-1">Actions</th>
                </tr>
            </thead>
            <tbody>
                <!-- DataTables will populate this -->
            </tbody>
        </table>
    </div>
</div>

<!-- Include Sales Drawer Component -->
<?= $this->include('Modules\SalesMonitoring\Views\components\sales_drawer') ?>

<!-- Include Inquiry Drawer Component -->
<?= $this->include('Modules\SalesMonitoring\Views\components\inquiry_drawer') ?>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>





<script>
// Global variables
let salesTable;
const eventId = <?= $event->eventid ?? 1 ?>;
console.log(eventId);

// Additional dashboard functionality
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize DataTables
    initializeSalesTable();
console.log('test');
    // Animate counters
    animateCounters();

    // Auto-refresh data every 30 seconds
    setInterval(refreshDashboardData, 30000);

    // Initialize filter event listeners
    initializeFilters();
});

function initializeSalesTable() {
    salesTable = $('#salesTable').DataTable({
        processing: true,
        serverSide: true,
        responsive: true,
        lengthChange: true,
        autoWidth: false,
        pageLength: 10,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        order: [[1, 'desc']], // Order by date descending
        ajax: {
            url: `<?= base_url('exhibitor/event/') ?>${eventId}/sales/data`,
            type: 'GET',
            data: function(d) {
                d.type_filter = $('#typeFilter').val();
                d.status_filter = $('#statusFilter').val();
                d.date_from = $('#dateFromFilter').val();
                d.date_to = $('#dateToFilter').val();
            },
            error: function(xhr, error, thrown) {
                console.error('DataTable Error:', error);
                showAlert('Failed to load sales data. Please refresh the page.', 'error');
            }
        },
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false, className: 'text-center' },
            { data: 'sale_date', name: 'sale_date' },
            {
                data: 'buyer_name',
                name: 'buyer_name',
                render: function(data, type, row) {
                    const avatar = `https://ui-avatars.com/api/?name=${encodeURIComponent(data)}&background=206bc4&color=fff`;
                    const country = row.country || 'Philippines';
                    return `
                        <div class="d-flex py-1 align-items-center">
                            <span class="avatar avatar-sm me-2" style="background-image: url(${avatar})"></span>
                            <div class="flex-fill">
                                <div class="font-weight-medium">${data}</div>
                                <div class="text-muted small">${country}</div>
                            </div>
                        </div>
                    `;
                }
            },
            { data: 'minor_prod_desc', name: 'minor_prod_desc' },
            {
                data: 'sale_type_badge',
                name: 'type',
                className: 'text-center',
                orderable: false
            },
            {
                data: 'cost',
                name: 'cost',
                className: 'text-end',
                render: function(data, type, row) {
                    const currency = row.type === 'export' ? '$' : '₱';
                    return `<span class="text-muted">${currency}${parseFloat(data).toLocaleString()}</span>`;
                }
            },
            {
                data: 'status',
                name: 'status',
                className: 'text-center',
                render: function(data, type, row) {
                    let badgeClass = 'bg-secondary';
                    switch(data.toLowerCase()) {
                        case 'booked':
                            badgeClass = 'status-booked';
                            break;
                        case 'under negotiation':
                            badgeClass = 'status-negotiation';
                            break;
                        case 'considered booked':
                            badgeClass = 'status-considered';
                            break;
                    }
                    return `<span class="badge ${badgeClass}">${data}</span>`;
                }
            },
            {
                data: 'action',
                name: 'action',
                orderable: false,
                searchable: false,
                className: 'text-center'
            }
        ],
        language: {
            processing: '<div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div>',
            emptyTable: 'No sales records found',
            zeroRecords: 'No matching sales records found'
        },
        buttons: [
            {
                extend: 'excel',
                text: 'Excel',
                className: 'btn btn-success d-none',
                exportOptions: {
                    columns: [1, 2, 3, 4, 5, 6] // Exclude # and Actions columns
                }
            },
            {
                extend: 'pdf',
                text: 'PDF',
                className: 'btn btn-danger d-none',
                exportOptions: {
                    columns: [1, 2, 3, 4, 5, 6] // Exclude # and Actions columns
                }
            }
        ],
        dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
             '<"row"<"col-sm-12"tr>>' +
             '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>' +
             'B', // Add buttons to DOM
        drawCallback: function() {
            // Reinitialize tooltips after table redraw
            $('[data-bs-toggle="tooltip"]').tooltip();
        }
    });
}

function initializeFilters() {
    // Filter change handlers
    $('#typeFilter, #statusFilter, #dateFromFilter, #dateToFilter').on('change', function() {
        salesTable.ajax.reload();
    });
}

function refreshSalesTable() {
    if (salesTable) {
        salesTable.ajax.reload(null, false);
        showAlert('Sales data refreshed', 'success');
    }
}

// Delete functionality
$(document).on('click', '.delete-sale', function(e) {
    e.preventDefault();
    const saleId = $(this).data('id');
    const row = $(this).closest('tr');

    Swal.fire({
        title: 'Delete Sale Record',
        text: 'Are you sure you want to delete this sale record? This action cannot be undone!',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d63384',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Yes, delete it!',
        cancelButtonText: 'Cancel'
    }).then((result) => {
        if (result.isConfirmed) {
            deleteSaleRecord(saleId);
        }
    });
});

function deleteSaleRecord(saleId) {
    // Show loading state
    Swal.fire({
        title: 'Deleting...',
        text: 'Please wait while we delete the sale record.',
        allowOutsideClick: false,
        allowEscapeKey: false,
        showConfirmButton: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });

    // Make AJAX request to delete
    $.ajax({
        url: `<?= base_url('exhibitor/event/') ?>${eventId}/sales/${saleId}/delete`,
        type: 'POST',
        data: {
            '<?= csrf_token() ?>': '<?= csrf_hash() ?>'
        },
        success: function(response) {
            if (response.status === 'success') {
                Swal.fire({
                    title: 'Deleted!',
                    text: 'Sale record has been deleted successfully.',
                    icon: 'success',
                    timer: 2000,
                    showConfirmButton: false
                });

                // Reload the table
                salesTable.ajax.reload();

                // Refresh dashboard stats
                refreshDashboardData();
            } else {
                Swal.fire({
                    title: 'Error!',
                    text: response.message || 'Failed to delete sale record.',
                    icon: 'error'
                });
            }
        },
        error: function(xhr, status, error) {
            Swal.fire({
                title: 'Error!',
                text: 'An error occurred while deleting the sale record. Please try again.',
                icon: 'error'
            });
        }
    });
}

// Edit functionality
$(document).on('click', '.edit-sale', function(e) {
    e.preventDefault();
    const saleId = $(this).data('id');
    const saleType = $(this).data('type');
    const eventId = $(this).data('event');

    // Open the sales drawer with edit mode
    openSalesDrawer();

    // Load sale data for editing
    loadSaleForEdit(saleId, eventId);
});

function loadSaleForEdit(saleId, eventId) {
    // Show loading in drawer
    showAlert('Loading sale data...', 'info');

    // Make AJAX request to get sale data
    $.ajax({
        url: `<?= base_url('exhibitor/event/') ?>${eventId}/sales/${saleId}/edit`,
        type: 'GET',
        success: function(response) {
            if (response.status === 'success') {
                populateEditForm(response.data);
            } else {
                showAlert('Failed to load sale data', 'error');
            }
        },
        error: function() {
            showAlert('Error loading sale data', 'error');
        }
    });
}

function populateEditForm(saleData) {
    // Populate form fields with sale data
    if (saleData.type) {
        selectSaleType(saleData.type);
    }

    setTimeout(() => {
        $('#saleDate').val(saleData.sale_date);
        $('#buyerName').val(saleData.buyer_name);
        $('#buyerType').val(saleData.buyer_type);
        $('#productCategory').val(saleData.minor_prod_desc);
        $('#saleCost').val(saleData.cost);
        $('#saleStatus').val(saleData.status);

        if (saleData.country) {
            $('#countryExport').val(saleData.country);
        }

        showAlert('Sale data loaded for editing', 'success');
    }, 500);
}

function animateCounters() {
    const counters = document.querySelectorAll('.h1');

    counters.forEach(counter => {
        const target = counter.innerText.replace(/[^0-9]/g, '');
        if (target && !isNaN(target)) {
            const increment = target / 100;
            let current = 0;

            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }

                // Preserve currency symbols and formatting
                const originalText = counter.innerText;
                const prefix = originalText.replace(/[0-9,]/g, '').split(target)[0];
                const suffix = originalText.replace(/[0-9,]/g, '').split(target)[1] || '';

                counter.innerText = prefix + Math.floor(current).toLocaleString() + suffix;
            }, 20);
        }
    });
}

function refreshDashboardData() {
    // Refresh statistics from server
    $.ajax({
        url: `<?= base_url('exhibitor/event/') ?>${eventId}/sales/statistics`,
        type: 'GET',
        success: function(response) {
            if (response.status === 'success') {
                updateDashboardStats(response.data);

                // Animate cards to show refresh
                const cards = document.querySelectorAll('.sales-card');
                cards.forEach((card, index) => {
                    setTimeout(() => {
                        card.style.transform = 'scale(1.02)';
                        setTimeout(() => {
                            card.style.transform = 'scale(1)';
                        }, 200);
                    }, index * 100);
                });

    // You can add actual AJAX call here to refresh data
                showAlert('Dashboard data refreshed', 'success');
            }
        },
        error: function() {
            showAlert('Failed to refresh dashboard data', 'error');
        }
    });
}

function updateDashboardStats(stats) {
    // Update the statistics cards with new data
    if (stats.total_sales !== undefined) {
        $('.total-sales .h1').text(stats.total_sales.toLocaleString());
    }
    if (stats.total_revenue !== undefined) {
        $('.total-revenue .h1').text('₱' + stats.total_revenue.toLocaleString());
    }
    if (stats.export_sales !== undefined) {
        $('.export-sales .h1').text('$' + stats.export_sales.toLocaleString());
    }
    if (stats.conversion_rate !== undefined) {
        $('.conversion-rate .h1').text(stats.conversion_rate + '%');
    }
}

// Export functionality
function exportToExcel() {
    if (salesTable) {
        showAlert('Exporting to Excel...', 'info');
        // Trigger DataTables Excel export
        salesTable.button('.buttons-excel').trigger();
    } else {
        showAlert('Table not initialized', 'error');
    }
}

function exportToPDF() {
    if (salesTable) {
        showAlert('Exporting to PDF...', 'info');
        // Trigger DataTables PDF export
        salesTable.button('.buttons-pdf').trigger();
    } else {
        showAlert('Table not initialized', 'error');
    }
}

// Utility function for alerts
function showAlert(message, type = 'info') {
    const alertClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    };

    const alertHtml = `
        <div class="alert ${alertClass[type]} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // Remove existing alerts
    $('.alert').remove();

    // Add new alert at the top of the page
    $('main .container-xl').prepend(alertHtml);

    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        $('.alert').fadeOut();
    }, 5000);
}

// Quick actions
function viewAllSales() {
    window.location.href = '<?= base_url('sales/all') ?>';
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + N to open new sale drawer
    if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
        e.preventDefault();
        openSalesDrawer();
    }

    // Ctrl/Cmd + R to refresh dashboard
    if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
        e.preventDefault();
        refreshDashboardData();
    }
});

// Add click handlers for table actions
document.addEventListener('click', function(e) {
    if (e.target.matches('.btn-outline-primary')) {
        const row = e.target.closest('tr');
        if (row) {
            // Get sale data from row and populate edit form
            editSale(row);
        }
    }
});

function editSale(row) {
    // Extract data from table row
    const cells = row.querySelectorAll('td');
    const saleData = {
        date: cells[0].textContent,
        client: cells[1].querySelector('.font-weight-medium').textContent,
        product: cells[2].textContent,
        type: cells[3].querySelector('.badge').textContent.toLowerCase(),
        amount: cells[4].textContent,
        status: cells[5].querySelector('.badge').textContent.toLowerCase()
    };

    // Open drawer with pre-filled data
    openSalesDrawer();

    // Populate form (you can enhance this based on your needs)
    setTimeout(() => {
        if (saleData.type) {
            selectSaleType(saleData.type);
        }

        document.getElementById('saleDate').value = new Date(saleData.date).toISOString().split('T')[0];
        document.getElementById('buyerName').value = saleData.client;

        // Parse and set amount
        const amount = saleData.amount.replace(/[^0-9.]/g, '');
        document.getElementById('saleCost').value = amount;

        showAlert('Editing existing sale', 'info');
    }, 500);
}
</script>
<?= $this->endSection() ?>
