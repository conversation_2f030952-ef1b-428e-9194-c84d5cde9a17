<?php

namespace Modules\SalesMonitoring\Controllers;

use App\Controllers\BaseController;

use Modules\SalesMonitoring\Models\ExhibitorProfileModel;
use Modules\SalesMonitoring\Models\InquiryModel;
use Modules\SalesMonitoring\Models\EventSchedModel;
use Modules\SalesMonitoring\Entities\Inquiry;

class InquiryController extends BaseController
{
    protected $inquiryModel;
    protected $exhibitorModel;
    protected $eventschedModel;

    public function __construct(){
        $this->inquiryModel = new InquiryModel();
        $this->exhibitorModel = new ExhibitorProfileModel();
        $this->eventschedModel = new EventSchedModel();
    }

    public function index()
    {
        //
    }

    public function store($event_id = null)
    {
        if (!$event_id) {
            return redirect()->to('exhibitor/');
        }

        // Load validation rules
        $validationConfig = new \Modules\SalesMonitoring\Config\Validation();
        $rules = $validationConfig->inquiryPost;
        $errors = $validationConfig->inquiryPost_errors;

        if ($this->validate($rules, $errors) === false) {
            if ($this->request->isAJAX()) {
                return $this->response->setJson(['status' => 'false', 'errors' => $this->validator->getErrors()]);
            }
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Process the data if validation passes
        $postData = $this->request->getPost();
        $data = $this->_prepareInquiryData($event_id, $postData);

        $inquiryEnt = new Inquiry();
        $inquiryEnt->fill($data);

        if ($this->inquiryModel->save($inquiryEnt)) {
            $last_id = $this->inquiryModel->insertID();
            if ($this->request->isAJAX()) {
                return $this->response->setJson(['status' => 'success', 'message' => 'Inquiry recorded successfully!', 'icon' => 'success', 'id' => $last_id]);
            }
            return redirect()->back()->with('success', 'Inquiry recorded successfully!');
        }

        // If save failed
        $saveErrors = ['general' => 'Failed to save the inquiry record. Please try again.'];
        if ($this->request->isAJAX()) {
            return $this->response->setJson(['status' => 'false', 'errors' => $saveErrors]);
        }
        return redirect()->back()->withInput()->with('errors', $saveErrors);
    }

    public function update($event_id = null, $inquiry_id = null)
    {
        if (!$inquiry_id) {
            return redirect()->to('exhibitor/');
        }

        $inquiry = $this->inquiryModel->find($inquiry_id);

        if (!$inquiry || $inquiry->ff_code !== session()->get('user_id')) {
            return $this->response->setJSON(['status' => 'error', 'message' => 'Unauthorized access']);
        }

        // Load validation rules
        $validationConfig = new \Modules\SalesMonitoring\Config\Validation();
        $rules = $validationConfig->inquiryPost;
        $errors = $validationConfig->inquiryPost_errors;

        if ($this->validate($rules, $errors) === false) {
            if ($this->request->isAJAX()) {
                return $this->response->setJson(['status' => 'false', 'errors' => $this->validator->getErrors()]);
            }
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $postData = $this->request->getPost();
        if ($this->request->getPost('event_id')) {
            $event_id = $this->request->getPost('event_id');
        } else {
            $event_id = $this->request->getGet('event_id');
        }

        $data = $this->_prepareInquiryData($event_id, $postData);

        $inquiryEnt = new Inquiry($data);
        $inquiryEnt->id = $inquiry_id;

        if ($this->inquiryModel->save($inquiryEnt)) {
            if ($this->request->isAJAX()) {
                return $this->response->setJson(['status' => 'success', 'message' => 'Inquiry record successfully updated.']);
            }
            return redirect()->back()->with('success', 'Inquiry record successfully updated.');
        }

        return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
    }

    public function delete($event_id = null, $inquiry_id = null) {
        if (!$inquiry_id) {
            return $this->response->setJSON(['status' => 'error', 'message' => 'Inquiry ID required']);
        }

        $inquiry = $this->inquiryModel->find($inquiry_id);

        if (!$inquiry || $inquiry->ff_code !== session()->get('user_id')) {
            return $this->response->setJSON(['status' => 'error', 'message' => 'Unauthorized access']);
        }

        if ($this->inquiryModel->delete($inquiry_id)) {
            return $this->response->setJSON(['status' => 'success', 'message' => 'Inquiry record successfully deleted']);
        }

        return $this->response->setJSON(['status' => 'error', 'message' => 'Failed to delete inquiry record']);
    }

    public function editForm($event_id = null, $inquiry_id = null)
    {
        if (!$inquiry_id) {
            return $this->response->setJSON(['status' => 'error', 'message' => 'No inquiry record specified']);
        }

        $inquiry = $this->inquiryModel->find($inquiry_id);

        if (!$inquiry) {
            return $this->response->setJSON(['status' => 'error', 'message' => 'Inquiry record not found']);
        }

        // Check if the record belongs to the current user
        if ($inquiry->ff_code !== session()->get('user_id')) {
            return $this->response->setJSON(['status' => 'error', 'message' => 'Unauthorized access']);
        }

        // Format date for display
        $inquiry->date = date('Y-m-d', strtotime($inquiry->date));

        return $this->response->setJSON(['status' => 'success', 'data' => $inquiry]);
    }

    private function _prepareInquiryData(int $event_id, array $data): array
    {
        $exhData = $this->exhibitorModel->where(['ff_code' => session()->get('user_id')])->first();
        $event = $this->eventschedModel->where(['eventid' => $event_id])->first();

        return [
            'ff_code' => $exhData->ff_code,
            'co_name' => $exhData->co_name,
            'date' => convert_to_mysql_date($data['inquiry_date'] ?? date('Y-m-d')),
            'buyers_met_no' => (int)($data['buyers_met_no'] ?? 0),
            'inquiry_no' => (int)($data['inquiry_no'] ?? 0),
            'faircode' => $event->faircode,
            'notes' => $data['notes'] ?? '',
            'created_by' => session()->get('user_id')
        ];
    }
}
