<!-- Inquiry Drawer Overlay -->
<div class="inquiry-drawer-overlay" id="inquiryDrawerOverlay" onclick="closeInquiryDrawer()"></div>

<!-- Inquiry Input Drawer -->
<div class="inquiry-drawer" id="inquiryDrawer">
    <div class="inquiry-drawer-header">
        <div class="d-flex align-items-center justify-content-between">
            <div>
                <br><br><br>
                <h3 class="mb-1">Add New Inquiry</h3>
                <p class="text-muted mb-0">Record your trade expo inquiry information</p>
            </div>
            <button type="button" class="btn-close" onclick="closeInquiryDrawer()"></button>
        </div>
    </div>
    
    <div class="inquiry-drawer-body">
        <form id="inquiryForm" novalidate>
            <?= csrf_field() ?>
            
            <!-- Basic Information Section -->
            <div class="form-section" id="inquiryBasicSection" data-aos="fade-up">
                <div class="form-section-title">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M8 7a4 4 0 1 0 8 0a4 4 0 0 0 -8 0"/><path d="M6 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2"/></svg>
                    Basic Information
                </div>
                
                <div class="row g-3">
                    <div class="col-md-12">
                        <label class="form-label required">Date</label>
                        <input type="date" class="form-control" id="inquiryDate" name="inquiry_date" required>
                        <div class="invalid-feedback"></div>
                    </div>
                </div>
            </div>

            <!-- Inquiry Details Section -->
            <div class="form-section" id="inquiryDetailsSection" data-aos="fade-up" data-aos-delay="100">
                <div class="form-section-title">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M9 11a3 3 0 1 0 6 0a3 3 0 0 0 -6 0"/><path d="M17.657 16.657l-4.243 4.243a2 2 0 0 1 -2.827 0l-4.244 -4.243a8 8 0 1 1 11.314 0z"/></svg>
                    Inquiry Details
                </div>
                
                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label required">Number of Buyers Met</label>
                        <input type="number" class="form-control" id="buyersMetNo" name="buyers_met_no" min="0" required>
                        <div class="invalid-feedback"></div>
                    </div>
                    
                    <div class="col-md-6">
                        <label class="form-label required">Number of Inquiries</label>
                        <input type="number" class="form-control" id="inquiryNo" name="inquiry_no" min="0" required>
                        <div class="invalid-feedback"></div>
                    </div>
                </div>
            </div>

            <!-- Notes Section -->
            <div class="form-section" id="inquiryNotesSection" data-aos="fade-up" data-aos-delay="200">
                <div class="form-section-title">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M9 7h6l2 2v9a2 2 0 0 1 -2 2h-10a2 2 0 0 1 -2 -2v-12a2 2 0 0 1 2 -2h4l2 2z"/><path d="M9 7v0a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v0"/></svg>
                    Additional Notes
                </div>
                
                <div class="row g-3">
                    <div class="col-12">
                        <label class="form-label">Notes (Optional)</label>
                        <textarea class="form-control" id="inquiryNotes" name="notes" rows="3" placeholder="Add any additional notes about the inquiries..."></textarea>
                        <div class="invalid-feedback"></div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    
    <div class="inquiry-drawer-footer">
        <div class="d-flex justify-content-between">
            <button type="button" class="btn btn-outline-secondary" onclick="closeInquiryDrawer()">
                Cancel
            </button>
            <div>
                <button type="button" class="btn btn-outline-primary me-2" onclick="saveInquiryDraft()">
                    Save as Draft
                </button>
                <button type="button" class="btn btn-primary" onclick="submitInquiry()">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M5 12l5 5l10 -10"/></svg>
                    Save Inquiry
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Inquiry Drawer Functionality
function openInquiryDrawer() {
    const drawer = document.getElementById('inquiryDrawer');
    const overlay = document.getElementById('inquiryDrawerOverlay');
    
    drawer.classList.add('show');
    overlay.classList.add('show');
    
    // Reset form
    resetInquiryForm();
    
    // Set today's date as default
    document.getElementById('inquiryDate').value = new Date().toISOString().split('T')[0];
    
    // Initialize AOS for drawer content
    AOS.refresh();
}

function closeInquiryDrawer() {
    const drawer = document.getElementById('inquiryDrawer');
    const overlay = document.getElementById('inquiryDrawerOverlay');
    
    drawer.classList.remove('show');
    overlay.classList.remove('show');
}

function resetInquiryForm() {
    const form = document.getElementById('inquiryForm');
    form.reset();

    // Clear all validation states
    const inputs = form.querySelectorAll('.form-control');
    inputs.forEach(input => {
        input.classList.remove('is-valid', 'is-invalid');
    });

    // Clear all invalid feedback messages
    const allFeedback = form.querySelectorAll('.invalid-feedback');
    allFeedback.forEach(feedback => {
        feedback.textContent = '';
        feedback.style.display = 'none';
    });
}

function validateInquiryForm() {
    const form = document.getElementById('inquiryForm');
    const date = document.getElementById('inquiryDate').value;
    const buyersMetNo = document.getElementById('buyersMetNo').value;
    const inquiryNo = document.getElementById('inquiryNo').value;

    // Basic validation
    if (!date) {
        showInquiryAlert('Please select a date', 'warning');
        return false;
    }

    if (!buyersMetNo || buyersMetNo < 0) {
        showInquiryAlert('Please enter a valid number of buyers met', 'warning');
        return false;
    }

    if (!inquiryNo || inquiryNo < 0) {
        showInquiryAlert('Please enter a valid number of inquiries', 'warning');
        return false;
    }

    return true;
}

function submitInquiry() {
    console.log('Submit inquiry clicked, submitting to server...');

    const submitBtn = event.target;
    submitBtn.classList.add('btn-loading');
    submitBtn.disabled = true;

    // Clear any previous validation errors
    clearInquiryValidationErrors();

    if (!validateInquiryForm()) {
        submitBtn.classList.remove('btn-loading');
        submitBtn.disabled = false;
        return;
    }

    const form = document.getElementById('inquiryForm');
    const formData = new FormData(form);

    const eventId = <?= $event_id ?? 1 ?>;

    // Submit to server
    fetch(`<?= base_url('exhibitor/event/') ?>${eventId}/inquiries`, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': '<?= csrf_hash() ?>'
        }
    })
    .then(response => response.json())
    .then(data => {
        submitBtn.classList.remove('btn-loading');
        submitBtn.disabled = false;

        if (data.status === 'success') {
            showInquiryAlert('Inquiry recorded successfully!', 'success');
            closeInquiryDrawer();
            
            // Refresh any inquiry-related data on the page
            if (typeof refreshInquiryData === 'function') {
                refreshInquiryData();
            }
        } else {
            // Handle validation errors
            if (data.errors) {
                displayInquiryValidationErrors(data.errors);
                showInquiryAlert('Please correct the errors below', 'warning');
            } else {
                showInquiryAlert(data.message || 'An error occurred while saving', 'error');
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        submitBtn.classList.remove('btn-loading');
        submitBtn.disabled = false;
        showInquiryAlert('An error occurred while saving. Please try again.', 'error');
    });
}

function saveInquiryDraft() {
    const formData = new FormData(document.getElementById('inquiryForm'));
    
    showInquiryAlert('Draft saved successfully!', 'info');
}

function showInquiryAlert(message, type) {
    // Create alert element
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // Insert at the top of the drawer body
    const drawerBody = document.querySelector('.inquiry-drawer-body');
    drawerBody.insertBefore(alertDiv, drawerBody.firstChild);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

function clearInquiryValidationErrors() {
    const form = document.getElementById('inquiryForm');
    
    // Remove validation classes
    const inputs = form.querySelectorAll('.form-control');
    inputs.forEach(input => {
        input.classList.remove('is-invalid');
    });
    
    // Clear feedback messages
    const feedbacks = form.querySelectorAll('.invalid-feedback');
    feedbacks.forEach(feedback => {
        feedback.textContent = '';
        feedback.style.display = 'none';
    });
}

function displayInquiryValidationErrors(errors) {
    Object.keys(errors).forEach(fieldName => {
        const field = document.querySelector(`[name="${fieldName}"]`);
        if (field) {
            field.classList.add('is-invalid');
            const feedback = field.nextElementSibling;
            if (feedback && feedback.classList.contains('invalid-feedback')) {
                feedback.textContent = errors[fieldName];
                feedback.style.display = 'block';
            }
        }
    });
}
</script>
