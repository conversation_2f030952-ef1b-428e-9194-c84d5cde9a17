<?php

namespace App\Controllers;

use App\Controllers\BaseController;

class SalesController extends BaseController
{
    public function index()
    {
        return $this->dashboard();
    }

    public function dashboard()
    {
        $data = [
            'title' => 'Sales Dashboard',
            'page_title' => 'Sales Monitoring Dashboard',
            'page_subtitle' => 'Track your sales performance and manage opportunities'
        ];

        return view('sales/dashboard_enhanced', $data);
    }

    public function create()
    {
        $data = [
            'title' => 'New Sale',
            'page_title' => 'Create New Sale',
            'page_subtitle' => 'Add a new sales entry to the system'
        ];

        return view('sales/create', $data);
    }

    public function list()
    {
        $data = [
            'title' => 'Sales List',
            'page_title' => 'All Sales',
            'page_subtitle' => 'View and manage all sales entries'
        ];

        return view('sales/list', $data);
    }

    public function export()
    {
        $data = [
            'title' => 'Export Sales',
            'page_title' => 'Export Sales',
            'page_subtitle' => 'Manage export sales and international clients'
        ];

        return view('sales/export', $data);
    }

    public function domestic()
    {
        $data = [
            'title' => 'Domestic Sales',
            'page_title' => 'Domestic Sales',
            'page_subtitle' => 'Manage domestic sales and local clients'
        ];

        return view('sales/domestic', $data);
    }

    public function settings()
    {
        $data = [
            'title' => 'Sales Settings',
            'page_title' => 'Sales Settings',
            'page_subtitle' => 'Configure sales dashboard settings'
        ];

        return view('sales/settings', $data);
    }

    public function samplemodal()
    {
        $data = [
            'title' => 'Sample Modal',
            'page_title' => 'Sample Modal',
            'page_subtitle' => 'Sample Modal'
        ];

        return view('sales/samplemodal', $data);
    }
}
